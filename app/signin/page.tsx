"use client";

import { Input } from "@nextui-org/react";
import PasswordInput from "../component/General/PasswordInput/HiddenInput";
import { signIn } from "./action";
import { useFormState, useFormStatus } from "react-dom";
import { useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import { useAuthStore } from "@/lib/zustand/zustand";
import { handleGoogleSignIn } from "../component/GoogleSigninButton";
import GoogleButton from "react-google-button";
import { handleAppleSignIn } from "../component/AppleSigninAction";

function SubmitButton() {
  const { pending } = useFormStatus();
  return (
    <button
      className="w-full text-white font-semibold py-6 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
      style={{
        backgroundColor: "#88D84D",
        border: "none",
      }}
      onMouseEnter={(e) => {
        if (!pending) {
          e.currentTarget.style.backgroundColor = "#7BC43D";
        }
      }}
      onMouseLeave={(e) => {
        if (!pending) {
          e.currentTarget.style.backgroundColor = "#88D84D";
        }
      }}
      type="submit"
      disabled={pending}
    >
      {pending ? "Signing in..." : "Log in"}
    </button>
  );
}

export default function Page() {
  const [state, formAction] = useFormState(signIn, null);
  const formRef = useRef<HTMLFormElement>(null);
  const router = useRouter();
  const { getUserWithoutSession } = useAuthStore();

  useEffect(() => {
    if (state?.error) {
      toast.error(state.error);
      formRef.current?.reset();
    }
    if (state?.success) {
      toast.success(state.success);
      getUserWithoutSession();
      router.push(state.redirectUrl || "/homepage");
      formRef.current?.reset();
    }
  }, [state, router]); // Removed getUserWithoutSession from dependencies

  return (
    <div className="min-h-screen bg-[#132435] text-white flex flex-col">
      {/* Sign-in Form */}
      <div className="flex flex-col items-center justify-start flex-grow px-4">
        <div className="w-full max-w-[650px] bg-[#132435] rounded-lg p-8 mt-10">
          <h1 className="text-4xl  text-center mb-4">Sign in</h1>
          <p className="text-xl  italic text-center text-gray-300 mb-6">
            Access insights, ask questions, and track research trends.
          </p>

          <form
            ref={formRef}
            action={formAction}
            className="w-full !text-white"
          >
            <Input
              className="mb-4"
              type="email"
              name="email"
              id="email"
              variant="bordered"
              label="Email"
              placeholder="Enter your email"
              classNames={{
                inputWrapper: "bg-[#132435] !text-white",
                input: "!text-white",
              }}
            />
            <PasswordInput
              placeholder="Enter your password"
              label="Password"
              name="password"
            />
            <SubmitButton />
          </form>

          {/* OR Divider */}
          <div className="w-full my-6 flex items-center ">
            <div className="flex-grow h-px bg-gray-600"></div>
            <span className="px-4 text-sm text-gray-400">or continue with</span>
            <div className="flex-grow h-px bg-gray-600"></div>
          </div>

          {/* Social Sign-In */}
          <div className="w-full flex gap-4 items-start justify-between">
            <GoogleButton className={"w-full"} onClick={handleGoogleSignIn} />
            <div
              onClick={handleAppleSignIn}
              className="bg-black text-white h-[50px] w-[240px] cursor-pointer hover:bg-gray-800 transition-colors inline-flex items-center justify-center rounded-lg font-medium"
            >
              Continue with Apple
            </div>
          </div>

          {/* Footer Links */}
          <div className="mt-6 text-sm text-center space-y-2 flex items-end flex-col">
            <a className="text-[#88D84D] hover:underline block" href="/signup">
              No account? Sign up here
            </a>
            <a
              className="text-[#88D84D] hover:underline block"
              href="/send-reset-email"
            >
              Forgot your password? Reset here
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
