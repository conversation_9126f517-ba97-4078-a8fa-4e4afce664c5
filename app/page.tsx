import HeroPanel from "./component/LandingPageComponent/HeroPanel";
import Features from "./component/LandingPageComponent/Features";
import Keywords from "./component/LandingPageComponent/Keywords";
import { WhoWeHelp } from "./component/LandingPageComponent/WhoOutreadHelps";

import { createClient } from "@/lib/supabase/server";
import Education from "./component/LandingPageComponent/LeftRight";
import AskDarwin from "./component/LandingPageComponent/AskDarwin";
import { GlobalCommunity } from "./component/LandingPageComponent/GlobalCommunity";
import { redirect } from "next/navigation";
import { TrustedBy } from "./component/LandingPageComponent/TrustedBy";
import Grid from "./component/LandingPageComponent/Grid";
import Identify from "./component/LandingPageComponent/Identify";
import FaqSection from "./component/LandingPageComponent/Faq";

export default async function Home() {
  const supabase = createClient();
  console.log("Getting user");

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (user) {
    redirect("/homepage");
  }
  console.log(user);

  return (
    <div className="w-full h-full bg-[#f9fafb]">
      <div className="flex flex-col w-[393px] lg:w-full h-full  text-black gap-y-[100px] lg:gap-y-[170px]">
        <HeroPanel />
        <TrustedBy />
        <Features />
        <Education />
        {/* <Keywords /> */}
        <Identify />
        {/* <AskDarwin /> */}
        <Grid />
        <WhoWeHelp />
        <GlobalCommunity />
        <FaqSection />
      </div>
    </div>
  );
}
