"use client";
import React, { useState, useEffect, useRef } from "react";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
import { Divider } from "@nextui-org/react";
import { useAuthStore } from "@/lib/zustand/zustand";
import { useRouter } from "next/navigation";

// Search icon component
const SearchIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
    className="w-5 h-5"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
    />
  </svg>
);

const AlternativeNavbar = ({ isPaidUser }: { isPaidUser: boolean }) => {
  const { user, isLoading, isAuthenticated } = useAuthStore((state) => state);
  const router = useRouter();
  const pathname = usePathname();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [addCustomButton, setAddCustomButton] = useState(
    pathname === "/darwinai" || pathname === "/history",
  );
  const [showTooltip, setShowTooltip] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState<{
    top: number;
    left: number;
  } | null>(null);
  const darwinAILinkRef = useRef<HTMLAnchorElement>(null);

  useEffect(() => {
    setAddCustomButton(pathname === "/darwinai" || pathname === "/history");
  }, [pathname]);

  useEffect(() => {
    if (isMenuOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }

    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isMenuOpen]);

  useEffect(() => {
    const hasSeenTooltip = localStorage.getItem("hasSeenTooltip");
    if (!hasSeenTooltip && pathname === "/homepage") {
      // Wait for DOM to render before calculating position
      setTimeout(() => {
        const darwinAILink = document.getElementById("desktop-darwinai-link");
        if (darwinAILink) {
          const rect = darwinAILink.getBoundingClientRect();
          setTooltipPosition({
            top: rect.bottom + window.scrollY + 10,
            left: rect.left + window.scrollX + rect.width / 2,
          });
          setShowTooltip(true);
          localStorage.setItem("hasSeenTooltip", "true");
        }
      }, 150); // Short delay to ensure DOM is ready
    }
  }, [pathname]);

  // Update position on resize/scroll
  useEffect(() => {
    if (showTooltip) {
      const handlePositionUpdate = () => {
        const darwinAILink = document.getElementById("desktop-darwinai-link");
        if (darwinAILink) {
          const rect = darwinAILink.getBoundingClientRect();
          setTooltipPosition({
            top: rect.bottom + window.scrollY + 10,
            left: rect.left + window.scrollX + rect.width / 2,
          });
        }
      };

      window.addEventListener("resize", handlePositionUpdate);
      window.addEventListener("scroll", handlePositionUpdate);

      return () => {
        window.removeEventListener("resize", handlePositionUpdate);
        window.removeEventListener("scroll", handlePositionUpdate);
      };
    }
  }, [showTooltip]);

  const NavLink: React.FC<{
    href: string;
    children: React.ReactNode;
    onClick?: () => void;
    id?: string;
  }> = ({ href, children, onClick, id }) => {
    const isActive = pathname === href;
    return (
      <Link
        href={href}
        className={`hover:text-gray-300 ${isActive ? "text-[#88D84D]" : ""}`}
        onClick={onClick}
        id={id}
      >
        {children}
      </Link>
    );
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const dismissTooltip = () => {
    setShowTooltip(false);
  };

  if (!isLoading && !isAuthenticated) {
    router.push("/signin");
  }

  if (isLoading) {
    return <></>;
  }

  return (
    <>
      <nav
        className={`bg-[#132435] text-white w-full p-4 px-12 lg:py-8 lg:px-32 `}
      >
        <div className="w-full flex justify-between items-center h-24">
          <div className="flex items-center">
            <Link href="/" className="flex-shrink-0">
              <Image
                src={`${pathname === "/darwinai" || pathname === "/history" ? "/white_logo.png" : "/white_logo.png"}`}
                alt="Outread Logo"
                width={120}
                height={40}
              />
            </Link>
          </div>
          <div className="hidden md:flex items-center space-x-4 lg:space-x-8">
            {addCustomButton ? (
              <>
                <NavLink href="/darwinai">DarwinAI</NavLink>
                <NavLink href="/history">History</NavLink>
                <NavLink href="/curie2">CurieAI</NavLink>
                <NavLink href="/settings">Settings</NavLink>
              </>
            ) : (
              <>
                <NavLink href="/curie2" id="desktop-darwinai-link">
                  CurieAI
                </NavLink>
                {/* <NavLink href="/darwinai" id="desktop-darwinai-link">
                    History
                  </NavLink> */}
                <NavLink href="/darwinai" id="desktop-darwinai-link">
                  DarwinAI
                </NavLink>
                <NavLink href="/settings">Settings</NavLink>
              </>
            )}
          </div>
          {/* Mobile Menu Toggle Button */}
          <div className="lg:hidden">
            <button
              onClick={toggleMenu}
              className={`${addCustomButton ? "text-[#132435]" : "text-white"}`}
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
         
              </svg>
            </button>
          </div>
        </div>
        {!addCustomButton && <Divider className="bg-gray-700"></Divider>}
      </nav>

      {/* Mobile Menu */}
      <div
        className={`fixed inset-0 z-50 ${isMenuOpen ? "block" : "hidden"}`}
        onClick={toggleMenu}
      >
        <div className="absolute inset-0 bg-[#132435] opacity-50 backdrop-blur-sm"></div>
        <div
          className="absolute top-0 left-0 w-3/4 h-full bg-[#132435] transform transition-transform duration-300 ease-in-out overflow-hidden"
          style={{
            transform: isMenuOpen ? "translateX(0)" : "translateX(-100%)",
          }}
          onClick={(e) => e.stopPropagation()}
        >
          <div className="p-5">
            <div className="flex justify-between items-center mb-5">
              <Image
                src="/white_logo.png"
                alt="Outread Logo"
                width={100}
                height={33}
              />
              <button onClick={toggleMenu} className="text-white">
                <svg
                  className="w-6 h-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
            <div className="flex flex-col space-y-4 text-sm">
              <NavLink href="/darwinai" onClick={toggleMenu}>
                DarwinAI
              </NavLink>
              <NavLink href="/history" onClick={toggleMenu}>
                History
              </NavLink>
              <NavLink href="/curie2" onClick={toggleMenu}>
                CurieAI
              </NavLink>
              <NavLink href="/settings" onClick={toggleMenu}>
                Settings
              </NavLink>
              {!isPaidUser && (
                <Link
                  href="https://apps.apple.com/us/app/outread/id6503236023?itscg=30200&itsct=apps_box_badge&mttnsubad=6503236023"
                  className="bg-[#88D84D] text-black px-3 py-1 rounded-full"
                >
                  Get App
                </Link>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default AlternativeNavbar;
